import {
  CarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CrownOutlined,
  ExclamationCircleOutlined,
  MinusCircleOutlined,
  RightOutlined,
  SettingOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import {
  Al<PERSON>,
  Button,
  Card,
  Col,
  Flex,
  List,
  message,
  Row,
  Spin,
  Tooltip,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { AuthService } from '@/services';
import { TeamService } from '@/services/team';
import type { TeamDetailResponse } from '@/types/api';
import {
  getTeamIdFromCurrentToken,
  hasTeamInCurrentToken,
  getUserIdFromCurrentToken,
} from '@/utils/tokenUtils';
import { recordTeamSelection, hasUserSelectedTeam } from '@/utils/teamSelectionUtils';

const { Text, Title } = Typography;
 



// 响应式布局样式
const styles = `
  .team-item .ant-card-body {
    padding: 0 !important;
  }

  .team-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
  }

  @media (max-width: 768px) {
    .team-item {
      margin-bottom: 8px;
    }

    .team-stats-row {
      margin-top: 8px;
    }

    .team-info-wrap {
      gap: 8px !important;
    }
  }

  @media (max-width: 576px) {
    .team-stats-row {
      margin-top: 12px;
    }

    .team-stats-col {
      margin-bottom: 4px;
    }

    .team-info-wrap {
      gap: 6px !important;
    }

    .team-meta-info {
      flex-wrap: wrap;
      gap: 6px !important;
    }

    .team-status-badges {
      flex-wrap: wrap;
      gap: 4px !important;
      margin-top: 4px;
    }
  }

  @media (max-width: 480px) {
    .team-name-text {
      font-size: 14px !important;
    }

    .team-meta-text {
      font-size: 11px !important;
    }

    .team-meta-info {
      gap: 4px !important;
    }

    .team-status-badges {
      gap: 3px !important;
    }
  }
`;

/**
 * 团队列表卡片组件
 *
 * 这是个人中心页面的核心组件，负责显示用户所属的团队列表，
 * 并提供团队切换、创建团队等功能。是团队管理系统的重要入口。
 *
 * 主要功能：
 * 1. 显示用户所属的所有团队
 * 2. 支持团队切换功能
 * 3. 支持创建新团队
 * 4. 显示当前选择的团队状态
 * 5. 处理团队切换过程中的状态管理
 *
 * 状态管理：
 * - 团队列表数据的获取和显示
 * - 团队切换过程的加载状态
 * - 创建团队模态框的状态
 * - 错误状态的处理和显示
 *
 * 团队切换逻辑：
 * 1. 检查用户登录状态
 * 2. 判断是否为当前团队（避免重复切换）
 * 3. 调用后端API进行团队切换
 * 4. 更新本地Token和全局状态
 * 5. 跳转到团队仪表盘
 *
 * 与全局状态的集成：
 * - 监听用户登录状态变化
 * - 同步团队切换后的状态更新
 * - 处理用户注销时的状态清理
 */
const TeamListCard: React.FC = () => {
  /**
   * 团队列表相关状态管理
   *
   * 这些状态用于管理团队列表的显示和交互：
   * - teams: 用户所属的团队列表数据
   * - loading: 团队列表加载状态
   * - error: 错误信息（如网络错误、权限错误等）
   * - switchingTeamId: 当前正在切换的团队ID（用于显示加载状态）
   */
  const [teams, setTeams] = useState<TeamDetailResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [switchingTeamId, setSwitchingTeamId] = useState<number | null>(null);

  /**
   * 创建团队功能已移至设置页面
   *
   * 为了更好的用户体验和功能组织，创建团队功能已经移动到
   * 专门的设置页面中。用户可以通过"团队设置"按钮跳转到
   * 设置页面进行团队创建和管理操作。
   */

  /**
   * 全局状态管理
   *
   * 从UmiJS全局状态中获取用户和团队信息：
   * - initialState: 包含当前用户和团队信息的全局状态
   * - setInitialState: 更新全局状态的函数
   * - currentTeam: 当前选择的团队信息
   */
  const { initialState, setInitialState } = useModel('@@initialState');
  const currentTeam = initialState?.currentTeam;

  /**
   * Token信息提取
   *
   * 从当前存储的Token中提取关键信息，用于状态判断和权限检查：
   * - currentTokenTeamId: Token中包含的团队ID
   * - currentUserId: Token中包含的用户ID
   * - hasTeamInToken: Token是否包含团队信息
   *
   * 这些信息用于：
   * - 判断当前是否已选择团队
   * - 确定哪个团队是当前激活的团队
   * - 记录用户的团队选择历史
   */
  const currentTokenTeamId = getTeamIdFromCurrentToken();
  const currentUserId = getUserIdFromCurrentToken();
  const hasTeamInToken = hasTeamInCurrentToken();

  // 判断是否有真正的当前团队：
  // 1. Token中有团队信息（说明用户已经选择过团队）
  // 2. initialState中有团队信息（说明已经获取过团队详情）
  // 3. 两者的团队ID一致（确保状态同步）
  // 4. 用户曾经主动选择过这个团队（区分初始登录和主动选择）
  const hasRealCurrentTeam = !!(
    hasTeamInToken &&
    currentTokenTeamId &&
    currentTeam &&
    currentTeam.id === currentTokenTeamId &&
    currentUserId &&
    hasUserSelectedTeam(currentUserId, currentTokenTeamId)
  );

  // 获取实际的当前团队ID：只有在用户真正选择过团队且状态同步时才返回团队ID
  const actualCurrentTeamId = hasRealCurrentTeam ? currentTokenTeamId : null;

  // 调试日志
  console.log('TeamListCard 状态调试:', {
    currentTeam: currentTeam?.id,
    currentTokenTeamId,
    currentUserId,
    hasTeamInToken,
    hasRealCurrentTeam,
    actualCurrentTeamId,
    hasUserSelectedCurrentTeam: currentUserId && currentTokenTeamId ? hasUserSelectedTeam(currentUserId, currentTokenTeamId) : false,
    initialStateCurrentUser: !!initialState?.currentUser,
  });

  // 获取团队列表数据
  useEffect(() => {
    const fetchTeams = async () => {
      try {
        setLoading(true);
        setError(null);
        const teamsData = await TeamService.getUserTeamsWithStats();
        setTeams(teamsData);
      } catch (error) {
        console.error('获取团队列表失败:', error);
        setError('获取团队列表失败');
      } finally {
        setLoading(false);
      }
    };

    // 只有在用户已登录时才获取团队列表
    if (initialState?.currentUser) {
      fetchTeams();
    }
  }, [initialState?.currentUser]);

  // 监听全局状态变化，处理注销等情况
  useEffect(() => {
    // 如果用户已注销（currentUser为undefined），清除本地团队列表状态
    if (!initialState?.currentUser) {
      setTeams([]);
      setError(null);
      setLoading(false);
      setSwitchingTeamId(null);
    }
  }, [initialState?.currentUser]);

  // 监听当前团队状态变化
  useEffect(() => {
    console.log('当前团队状态变化:', {
      currentTeam: currentTeam?.id,
      actualCurrentTeamId,
      hasRealCurrentTeam,
    });
  }, [currentTeam?.id, actualCurrentTeamId, hasRealCurrentTeam]);

  // 创建团队功能已移至设置页面，此处不再需要处理函数

  /**
   * 团队切换处理函数
   *
   * 这是团队切换功能的核心函数，处理用户从一个团队切换到另一个团队的完整流程。
   * 包括权限检查、API调用、状态更新、页面跳转等步骤。
   *
   * 切换流程：
   * 1. 用户登录状态检查
   * 2. 当前团队状态判断（避免重复切换）
   * 3. 调用后端团队选择API
   * 4. 验证切换结果
   * 5. 更新本地Token和全局状态
   * 6. 记录用户选择历史
   * 7. 跳转到团队仪表盘
   *
   * 状态管理：
   * - 设置切换加载状态（防止重复点击）
   * - 更新全局用户和团队状态
   * - 处理切换过程中的错误状态
   *
   * 错误处理：
   * - 网络错误：显示网络连接提示
   * - 权限错误：由响应拦截器统一处理
   * - 业务错误：显示具体的错误信息
   *
   * @param teamId 要切换到的团队ID
   * @param teamName 团队名称（用于显示消息）
   */
  const handleTeamSwitch = async (teamId: number, teamName: string) => {
    /**
     * 用户登录状态检查
     *
     * 确保用户已登录才能进行团队切换操作。
     * 虽然组件层面已有登录检查，但这里再次确认以确保安全性。
     */
    if (!initialState?.currentUser) {
      return;
    }

    try {
      /**
       * 设置切换状态
       *
       * 标记当前正在切换的团队ID，用于：
       * 1. 在UI上显示加载状态
       * 2. 防止用户重复点击
       * 3. 提供视觉反馈
       */
      setSwitchingTeamId(teamId);

      /**
       * 当前团队检查
       *
       * 如果用户点击的是当前已选择的团队，直接跳转到仪表盘，
       * 避免不必要的API调用和Token更新。
       */
      if (teamId === actualCurrentTeamId) {
        history.push('/dashboard');
        return;
      }

      /**
       * 执行团队切换API调用
       *
       * 调用后端的团队选择接口，后端会：
       * 1. 验证用户是否有权限访问该团队
       * 2. 生成包含新团队信息的JWT Token
       * 3. 返回团队详细信息和切换状态
       */
      const response = await AuthService.selectTeam({ teamId });

      /**
       * 验证切换结果
       *
       * 检查后端返回的响应是否表示切换成功：
       * - teamSelectionSuccess: 切换成功标识
       * - team: 新团队的详细信息
       * - team.id: 确认返回的团队ID与请求的一致
       */
      if (
        response.teamSelectionSuccess &&
        response.team &&
        response.team.id === teamId
      ) {
        /**
         * 记录用户选择历史
         *
         * 将用户的团队选择记录到本地存储，用于：
         * - 下次登录时的默认团队选择
         * - 用户行为分析
         * - 提升用户体验
         */
        if (currentUserId) {
          recordTeamSelection(currentUserId, teamId);
        }

        /**
         * 异步更新全局状态
         *
         * 由于Token已经更新，需要同步更新全局状态中的用户和团队信息。
         * 使用异步更新避免阻塞页面跳转，提升用户体验。
         *
         * 更新流程：
         * 1. 并行获取最新的用户信息和团队信息
         * 2. 验证获取的团队信息是否正确
         * 3. 更新全局状态
         * 4. 处理更新过程中的错误
         */
        if (
          initialState?.fetchTeamInfo &&
          initialState?.fetchUserInfo &&
          setInitialState
        ) {
          // 异步更新状态，不阻塞跳转
          Promise.all([
            initialState.fetchUserInfo(),
            initialState.fetchTeamInfo(),
          ])
            .then(([currentUser, currentTeam]) => {
              // 确认获取的团队信息与切换的团队一致
              if (currentTeam && currentTeam.id === teamId) {
                setInitialState({
                  ...initialState,
                  currentUser,
                  currentTeam,
                });
              }
            })
            .catch((error) => {
              console.error('更新 initialState 失败:', error);
              // 状态更新失败不影响团队切换的核心功能
            });
        }

        /**
         * 页面跳转
         *
         * 切换成功后跳转到团队仪表盘。
         * 路由守卫会验证新的Token并允许访问团队页面。
         */
        history.push('/dashboard');
      } else {
        /**
         * 切换失败处理
         *
         * 如果后端返回的响应不符合预期，说明切换失败。
         * 记录错误日志并提示用户重试。
         */
        // 团队切换响应异常，未返回正确的团队信息
      }
    } catch (error: any) {
      /**
       * 异常处理
       *
       * 处理团队切换过程中可能出现的各种异常：
       * - 网络错误：连接超时、服务器不可达等
       * - 权限错误：用户无权限访问该团队
       * - 业务错误：团队不存在、状态异常等
       *
       * 错误处理策略：
       * 1. 记录详细的错误日志用于调试
       * 2. 响应拦截器已处理大部分错误消息
       * 3. 只对网络错误显示通用提示
       */
      // 错误处理由响应拦截器统一处理
    } finally {
      /**
       * 清理切换状态
       *
       * 无论切换成功还是失败，都要清除切换状态，
       * 恢复UI的正常状态，允许用户进行下一次操作。
       */
      setSwitchingTeamId(null);
    }
  };

  return (
    <>
      {/* 注入样式 */}
      <style dangerouslySetInnerHTML={{ __html: styles }} />

      <Card
        className="dashboard-card"
        style={{
          borderRadius: 16,
          boxShadow: '0 6px 20px rgba(0,0,0,0.08)',
          border: 'none',
          background: 'linear-gradient(145deg, #ffffff, #f8faff)',
        }}
        title={
          <Flex justify="space-between" align="center">
            <Title
              level={4}
              style={{
                margin: 0,
                background: 'linear-gradient(135deg, #1890ff, #722ed1)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: 600,
              }}
            >
              团队列表
            </Title>
            <Button
              type="default"
              icon={<SettingOutlined />}
              onClick={() => history.push('/settings')}
              style={{
                borderRadius: 8,
                border: '1px solid #d9d9d9',
              }}
            >
              团队设置
            </Button>
          </Flex>
        }
      >
        {error ? (
          <Alert
            message="团队列表加载失败"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        ) : (
          <Spin spinning={loading}>
            {!initialState?.currentUser ? (
              <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                <Text type="secondary">请先登录以查看团队列表</Text>
              </div>
            ) : teams.length === 0 && !loading ? (
              <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                <Text type="secondary">暂无团队，请先加入或创建团队</Text>
              </div>
            ) : (
              <List
                dataSource={teams}
                renderItem={(item) => (
                  <List.Item>
                    <Card
                      className="team-item"
                      style={{
                        background:
                          actualCurrentTeamId === item.id
                            ? 'linear-gradient(135deg, #f0f9ff, #e6f4ff)'
                            : '#fff',
                        borderRadius: 8,
                        boxShadow:
                          actualCurrentTeamId === item.id
                            ? '0 2px 8px rgba(24, 144, 255, 0.12)'
                            : '0 1px 4px rgba(0,0,0,0.06)',
                        width: '100%',
                        borderLeft: `3px solid ${item.isCreator ? '#722ed1' : '#52c41a'}`,
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        border:
                          actualCurrentTeamId === item.id
                            ? '1px solid #91caff'
                            : '1px solid #f0f0f0',
                        padding: '12px 16px',
                        position: 'relative',
                        overflow: 'hidden',
                      }}
                      hoverable
                      onMouseEnter={(e) => {
                        if (actualCurrentTeamId !== item.id) {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow =
                            '0 8px 24px rgba(0,0,0,0.12)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (actualCurrentTeamId !== item.id) {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow =
                            '0 2px 8px rgba(0,0,0,0.06)';
                        }
                      }}
                    >
                      {/* 响应式布局 */}
                      <Row
                        gutter={[8, 8]}
                        align="middle"
                        style={{ width: '100%' }}
                      >
                        {/* 左侧：团队信息 */}
                        <Col xs={24} sm={24} md={14} lg={12} xl={14}>
                          <Flex vertical gap={6} className="team-info-wrap">
                            {/* 团队名称行 */}
                            <Flex align="center" gap={8} wrap="wrap">
                              <div
                                style={{
                                  cursor: 'pointer',
                                  padding: '2px 4px',
                                  borderRadius: 4,
                                  transition: 'all 0.2s ease',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 6,
                                }}
                                onClick={() =>
                                  handleTeamSwitch(item.id, item.name)
                                }
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.background =
                                    'rgba(24, 144, 255, 0.05)';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.background =
                                    'transparent';
                                }}
                              >
                                <Text
                                  strong
                                  style={{
                                    fontSize: 16,
                                    color:
                                      actualCurrentTeamId === item.id
                                        ? '#1890ff'
                                        : '#262626',
                                    lineHeight: 1.2,
                                  }}
                                >
                                  {item.name}
                                </Text>
                                <RightOutlined
                                  style={{
                                    fontSize: 10,
                                    color:
                                      actualCurrentTeamId === item.id
                                        ? '#1890ff'
                                        : '#8c8c8c',
                                    verticalAlign: 'middle',
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                  }}
                                />
                              </div>

                              {/* 状态标识 */}
                              {actualCurrentTeamId === item.id && (
                                <span
                                  style={{
                                    background: '#1890ff',
                                    color: 'white',
                                    padding: '1px 6px',
                                    borderRadius: 8,
                                    fontSize: 10,
                                    fontWeight: 500,
                                  }}
                                >
                                  当前
                                </span>
                              )}



                              {switchingTeamId === item.id && (
                                <Flex align="center" gap={4}>
                                  <Spin size="small" />
                                  <Text style={{ fontSize: 10, color: '#666' }}>
                                    切换中
                                  </Text>
                                </Flex>
                              )}
                            </Flex>

                            {/* 团队基本信息 */}
                            <Flex align="center" gap={12} wrap="wrap" className="team-meta-info">
                              <Tooltip
                                title={`团队创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`}
                              >
                                <Flex align="center" gap={4}>
                                  <ClockCircleOutlined
                                    style={{ color: '#8c8c8c', fontSize: 12 }}
                                  />
                                  <Text
                                    style={{ fontSize: 12, color: '#8c8c8c' }}
                                  >
                                    创建: {new Date(
                                      item.createdAt,
                                    ).toLocaleDateString('zh-CN')}
                                  </Text>
                                </Flex>
                              </Tooltip>

                              {/* 加入日期 */}
                              {item.assignedAt && (
                                <Tooltip
                                  title={`加入团队时间: ${new Date(item.assignedAt).toLocaleString('zh-CN')}`}
                                >
                                  <Flex align="center" gap={4}>
                                    <UserOutlined
                                      style={{ color: '#8c8c8c', fontSize: 12 }}
                                    />
                                    <Text
                                      style={{ fontSize: 12, color: '#8c8c8c' }}
                                    >
                                      加入: {new Date(
                                        item.assignedAt,
                                      ).toLocaleDateString('zh-CN')}
                                    </Text>
                                  </Flex>
                                </Tooltip>
                              )}

                              <Tooltip
                                title={`团队成员: ${item.memberCount}人`}
                              >
                                <Flex align="center" gap={4}>
                                  <TeamOutlined
                                    style={{ color: '#8c8c8c', fontSize: 12 }}
                                  />
                                  <Text
                                    style={{ fontSize: 12, color: '#8c8c8c' }}
                                  >
                                    {item.memberCount} 人
                                  </Text>
                                </Flex>
                              </Tooltip>
                            </Flex>

                            {/* 状态标识行 */}
                            <Flex align="center" gap={8} wrap="wrap" className="team-status-badges">
                              {/* 角色标识 */}
                              <span
                                style={{
                                  background: item.isCreator
                                    ? '#722ed1'
                                    : '#52c41a',
                                  color: 'white',
                                  padding: '2px 6px',
                                  borderRadius: 8,
                                  fontSize: 10,
                                  fontWeight: 500,
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 2,
                                }}
                              >
                                {item.isCreator ? (
                                  <>
                                    <CrownOutlined style={{ fontSize: 9 }} />
                                    管理员
                                  </>
                                ) : (
                                  <>
                                    <UserOutlined style={{ fontSize: 9 }} />
                                    成员
                                  </>
                                )}
                              </span>

                              {/* 用户状态标识 */}
                              <span
                                style={{
                                  background: item.isActive ? '#52c41a' : '#ff4d4f',
                                  color: 'white',
                                  padding: '2px 6px',
                                  borderRadius: 8,
                                  fontSize: 10,
                                  fontWeight: 500,
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 2,
                                }}
                              >
                                {item.isActive ? (
                                  <>
                                    <CheckCircleOutlined style={{ fontSize: 9 }} />
                                    启用
                                  </>
                                ) : (
                                  <>
                                    <MinusCircleOutlined style={{ fontSize: 9 }} />
                                    停用
                                  </>
                                )}
                              </span>
                            </Flex>
                          </Flex>
                        </Col>

                        {/* 右侧：响应式指标卡片 */}
                        <Col xs={24} sm={24} md={10} lg={12} xl={10}>
                          <Row
                            gutter={[4, 4]}
                            justify={{ xs: 'start', md: 'end' }}
                          >
                            {/* 车辆资源 */}
                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                              <div
                                style={{
                                  background: '#f0f7ff',
                                  border: '1px solid #d9e8ff',
                                  borderRadius: 6,
                                  padding: '4px 6px',
                                  textAlign: 'center',
                                  minWidth: '45px',
                                }}
                              >
                                <Flex vertical align="center" gap={1}>
                                  <CarOutlined
                                    style={{ color: '#1890ff', fontSize: 12 }}
                                  />
                                  <Text
                                    strong
                                    style={{
                                      fontSize: 14,
                                      color: '#1890ff',
                                      lineHeight: 1,
                                    }}
                                  >
                                    {item.stats?.vehicles || 0}
                                  </Text>
                                  <Text style={{ fontSize: 8, color: '#666' }}>
                                    车辆
                                  </Text>
                                </Flex>
                              </div>
                            </Col>

                            {/* 人员资源 */}
                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                              <div
                                style={{
                                  background: '#f6ffed',
                                  border: '1px solid #d1f0be',
                                  borderRadius: 6,
                                  padding: '4px 6px',
                                  textAlign: 'center',
                                  minWidth: '45px',
                                }}
                              >
                                <Flex vertical align="center" gap={1}>
                                  <UserOutlined
                                    style={{ color: '#52c41a', fontSize: 12 }}
                                  />
                                  <Text
                                    strong
                                    style={{
                                      fontSize: 14,
                                      color: '#52c41a',
                                      lineHeight: 1,
                                    }}
                                  >
                                    {item.stats?.personnel || 0}
                                  </Text>
                                  <Text style={{ fontSize: 8, color: '#666' }}>
                                    人员
                                  </Text>
                                </Flex>
                              </div>
                            </Col>

                            {/* 临期事项 */}
                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                              <div
                                style={{
                                  background: '#fff7e6',
                                  border: '1px solid #ffd666',
                                  borderRadius: 6,
                                  padding: '4px 6px',
                                  textAlign: 'center',
                                  minWidth: '45px',
                                }}
                              >
                                <Flex vertical align="center" gap={1}>
                                  <ExclamationCircleOutlined
                                    style={{ color: '#faad14', fontSize: 12 }}
                                  />
                                  <Text
                                    strong
                                    style={{
                                      fontSize: 14,
                                      color: '#faad14',
                                      lineHeight: 1,
                                    }}
                                  >
                                    {item.stats?.expiring || 0}
                                  </Text>
                                  <Text style={{ fontSize: 8, color: '#666' }}>
                                    临期
                                  </Text>
                                </Flex>
                              </div>
                            </Col>

                            {/* 逾期事项 */}
                            <Col xs={6} sm={6} md={6} lg={6} xl={6}>
                              <div
                                style={{
                                  background: '#fff1f0',
                                  border: '1px solid #ffccc7',
                                  borderRadius: 6,
                                  padding: '4px 6px',
                                  textAlign: 'center',
                                  minWidth: '45px',
                                }}
                              >
                                <Flex vertical align="center" gap={1}>
                                  <ExclamationCircleOutlined
                                    style={{ color: '#ff4d4f', fontSize: 12 }}
                                  />
                                  <Text
                                    strong
                                    style={{
                                      fontSize: 14,
                                      color: '#ff4d4f',
                                      lineHeight: 1,
                                    }}
                                  >
                                    {item.stats?.overdue || 0}
                                  </Text>
                                  <Text style={{ fontSize: 8, color: '#666' }}>
                                    逾期
                                  </Text>
                                </Flex>
                              </div>
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                    </Card>
                  </List.Item>
                )}
              />
            )}
          </Spin>
        )}
      </Card>

      {/* 创建团队功能已移至设置页面 */}
    </>
  );
};

export default TeamListCard;
